<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net472</TargetFramework>
    <LangVersion>latest</LangVersion>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <OutputPath>Assemblies\</OutputPath>
    
    <AssemblyName>NationalSystemMod</AssemblyName>
    <RootNamespace>AIGen.NationalSystemMod</RootNamespace>
    
    <!-- This is the path to your RimWorld installation. -->
    <RimWorldPath>D:\Program Files (x86)\Steam\steamapps\common\RimWorld</RimWorldPath>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="Assembly-CSharp">
      <HintPath>$(RimWorldPath)\RimWorldWin64_Data\Managed\Assembly-CSharp.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="UnityEngine.Core">
      <HintPath>$(RimWorldPath)\RimWorldWin64_Data\Managed\UnityEngine.Core.dll</HintPath>
      <Private>false</Private>
    </Reference>
     <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>$(RimWorldPath)\RimWorldWin64_Data\Managed\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>false</Private>
    </Reference>
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Krafs.Publicizer" Version="2.2.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <Publicize Include="Assembly-CSharp" />
  </ItemGroup>

</Project> 