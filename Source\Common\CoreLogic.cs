using Verse; // Core RimWorld namespace for basic utilities and logging.
using RimWorld; // For Faction and other RimWorld types
using RimWorld.Planet; // Needed for World and settlements.
using System.Collections.Generic; // Useful for collections, though not heavily used here yet.
using System.Linq; // For LINQ queries, use sparingly if performance critical.
using AIGen.NationalSystemMod.World; // Add this using directive.
using AIGen.NationalSystemMod.Nation; // NEW: Add this using directive.
using AIGen.NationalSystemMod.Defs; // NEW: Add this for NationalPolicyDef.

namespace AIGen.NationalSystemMod.Common // IMPORTANT: Use your actual package ID and include .Common.
{
    // This class is static because it will hold global logic and won't need instances.
    public static class CoreLogic 
    {
        // A simple flag to track if our mod's main systems have been initialized.
        private static bool _isModSystemInitialized = false;

        // Public property to check the initialization status from anywhere in the mod.
        public static bool IsModSystemInitialized => _isModSystemInitialized;

        // NEW: Instance of our WorldInfluenceMap to manage influence data globally.
        public static World.WorldInfluenceMap WorldInfluenceMap { get; private set; }

        // NEW: Reference to the diplomacy tracker.
        public static World.FactionDiplomacyTracker DiplomacyTracker { get; private set; }

        // NEW: Dictionary to store NationalData for each Faction.
        // This will be managed by a GameComponent for persistence.
        private static Dictionary<Faction, NationalData> _nationalDataByFaction;
        public static Dictionary<Faction, NationalData> NationalDataByFaction => _nationalDataByFaction;

        // NEW: Internal GameComponent for CoreLogic's persistent data (like NationalData).
        // This will allow us to save/load the _nationalDataByFaction dictionary.
        public class CoreLogicGameComponent : GameComponent
        {
            public CoreLogicGameComponent() { } // GameComponent constructor

            public override void ExposeData()
            {
                base.ExposeData();
                // Scribe _nationalDataByFaction using LookMode.Reference for Faction and LookMode.Deep for NationalData.
                Scribe_Collections.Look(ref _nationalDataByFaction, "nationalDataByFaction", LookMode.Reference, LookMode.Deep);

                // If loading from an old save where _nationalDataByFaction was null, initialize it.
                if (Scribe.mode == LoadSaveMode.PostLoadInit && _nationalDataByFaction == null)
                {
                    _nationalDataByFaction = new Dictionary<Faction, NationalData>();
                }
            }
        }

        // This method will be called once to perform major, one-time setup tasks for the mod's systems.
        // It should ideally be invoked after the game world has fully loaded to ensure all necessary
        // game data is available.
        public static void InitializeModSystems()
        {
            if (_isModSystemInitialized) // Prevent re-initialization if already done.
            {
                return;
            }

            Log.Message("[NationalSystemMod.CoreLogic] Core systems initializing...");

            // NEW: Initialize the WorldInfluenceMap instance.
            // This is crucial for saving/loading data via IExposable.
            WorldInfluenceMap = Current.Game.GetComponent<World.WorldInfluenceMap>();
            if (WorldInfluenceMap == null)
            {
                WorldInfluenceMap = new World.WorldInfluenceMap();
                Current.Game.components.Add(WorldInfluenceMap);
            }

            // NEW: Get or add FactionDiplomacyTracker.
            DiplomacyTracker = Find.World.GetComponent<World.FactionDiplomacyTracker>();
            if (DiplomacyTracker == null)
            {
                DiplomacyTracker = new World.FactionDiplomacyTracker(Find.World);
                Find.World.components.Add(DiplomacyTracker);
            }

            // NEW: Get or add CoreLogic's own GameComponent for persistent data.
            var coreLogicComp = Current.Game.GetComponent<CoreLogicGameComponent>();
            if (coreLogicComp == null)
            {
                Current.Game.components.Add(new CoreLogicGameComponent());
            }
            
            // Ensure NationalData exists for all relevant factions at game start.
            // This covers new games and factions appearing during gameplay.
            EnsureNationalDataExistsForFactions();

            // NEW: Assign policies if not already assigned (for new games or old saves).
            AssignPoliciesToFactions();

            // NEW: Perform initial influence calculation after game world has loaded.
            // This should only happen once per game session.
            CalculateInitialWorldInfluence();
            //NationalBorderDrawer.MarkCacheDirty(); // NEW: Mark border cache dirty after initial influence calculation.

            _isModSystemInitialized = true; // Mark as initialized after setup.
            Log.Message("[NationalSystemMod.CoreLogic] Core systems initialized.");
        }

        // NEW: Ensure NationalData exists for all playable/relevant factions.
        private static void EnsureNationalDataExistsForFactions()
        {
            if (_nationalDataByFaction == null)
            {
                _nationalDataByFaction = new Dictionary<Faction, NationalData>();
            }

            foreach (Faction faction in Find.FactionManager.AllFactions)
            {
                if (faction.def.permanentEnemy || faction.Hidden || faction.IsPlayer) continue;

                if (!_nationalDataByFaction.ContainsKey(faction))
                {
                    _nationalDataByFaction.Add(faction, new NationalData(faction));
                    Log.Message($"[NationalSystemMod.CoreLogic] Created NationalData for faction: {faction.Name}");
                }
            }
        }

        // NEW: Assign NationalPolicies to factions.
        private static void AssignPoliciesToFactions()
        {
            List<NationalPolicyDef> allPolicies = DefDatabase<NationalPolicyDef>.AllDefsListForReading;
            if (allPolicies.Count == 0)
            {
                Log.Warning("[NationalSystemMod.CoreLogic] No NationalPolicyDefs found in DefDatabase! AI behavior will be default.");
                return;
            }

            foreach (var entry in _nationalDataByFaction)
            {
                Faction faction = entry.Key;
                NationalData nationalData = entry.Value;

                // Player faction doesn't get an AI policy (they control their actions).
                if (faction.IsPlayer)
                {
                    // Optionally, set a "player default" policy or allow player to choose.
                    // For now, leave player policy as null or a specific "player controlled" def.
                    continue; 
                }

                if (nationalData.CurrentPolicy == null) // Only assign if not already assigned.
                {
                    nationalData.CurrentPolicy = allPolicies.RandomElement();
                    Log.Message($"[NationalSystemMod.CoreLogic] Assigned policy '{nationalData.CurrentPolicy.label}' to {faction.Name}.");
                }
            }
        }

        // --- NEW METHOD: CalculateInitialWorldInfluence() ---
        // This method will iterate through existing settlements and assign them basic influence.
        // This is a one-time calculation during mod initialization, NOT a tick-based one.
        private static void CalculateInitialWorldInfluence()
        {
            Log.Message("[NationalSystemMod.CoreLogic] Calculating initial world influence...");

            // Iterate through all settlements on the world map.
            // Ensure this is optimized for potentially many settlements.
            foreach (Settlement settlement in Find.WorldObjects.Settlements)
            {
                // We only care about settlements owned by factions. Player colonies are settlements too.
                if (settlement.Faction != null)
                {
                    // For initial setup, let's give them influence over their own tile
                    // and a few adjacent tiles. This can be refined later.
                    float baseInfluence = 0.5f; // A simple base influence value.

                    // Influence the settlement's own tile
                    WorldInfluenceMap.SetInfluenceData(settlement.Tile, settlement.Faction, baseInfluence);

                    // Influence adjacent tiles - simplified for now
                    // TODO: Implement proper adjacent tile calculation

                    // TODO: Add adjacent tile influence when proper API is available
                }
            }
            Log.Message("[NationalSystemMod.CoreLogic] Initial world influence calculation complete.");
            NationalBorderDrawer.MarkCacheDirty(); // NEW: Mark border cache dirty after initial calculation.
        }

        // This method is a placeholder for periodic updates, like daily or weekly calculations.
        // It should NOT be called every game tick. This is crucial for performance.
        // Logic for things like daily influence changes, economic summaries, or long-term diplomatic shifts can go here.
        public static void PeriodicUpdate()
        {
            if (!IsModSystemInitialized) return;

            // This is called daily (or at your defined interval).
            InfluenceManager.UpdateInfluence(); // Handles AI influence spread too.

            // NEW: Trigger AI War Logic periodically.
            if (GenTicks.TicksGame % (GenDate.TicksPerDay * 5) == 0) // Every 5 days, AI checks for war.
            {
                DiplomacyTracker.AIDeclareWarLogic();
            }

            // Future: Other periodic national system updates.
        }

        // NEW: Method to update NationalData based on WorldInfluenceMap.
        // This aggregates influence from all owned tiles for each faction.
        private static void UpdateNationalDataFromInfluenceMap()
        {
            //Log.Message("[NationalSystemMod.CoreLogic] Updating NationalData from Influence Map...");
            
            // Temporarily reset influence scores for all factions.
            foreach (NationalData nd in _nationalDataByFaction.Values)
            {
                nd.TotalInfluenceScore = 0f;
                nd.ControlledTilesCount = 0;
            }

            // Iterate through influenced tiles and aggregate data.
            if(WorldInfluenceMap != null && WorldInfluenceMap.InfluenceDataByTile != null)
            {
                foreach (var tileEntry in WorldInfluenceMap.InfluenceDataByTile)
                {
                    InfluenceTileData data = tileEntry.Value;
                    if (data?.faction != null)
                    {
                        if (_nationalDataByFaction.TryGetValue(data.faction, out NationalData nationalData))
                        {
                            nationalData.TotalInfluenceScore += data.influenceAmount;
                            nationalData.ControlledTilesCount++;
                        }
                    }
                }
            }
            

            // After aggregation, potentially call RecalculateDerivedStats for each NationalData.
            foreach (NationalData nd in _nationalDataByFaction.Values)
            {
                nd.RecalculateDerivedStats(); // Update other stats based on new influence score.
            }

            //Log.Message("[NationalSystemMod.CoreLogic] NationalData update complete.");
        }

        // This method is an example of an event-driven hook. It should be called by specific game events,
        // not on a regular timer. This is highly efficient for reacting to specific occurrences.
        public static void OnSettlementCreated(Faction faction, Settlement settlement)
        {
            if (!IsModSystemInitialized) return;
            Log.Message($"[NationalSystemMod.CoreLogic] New settlement created: {settlement.Label} by {faction.Name}");

            // NEW: When a new settlement is created, give it immediate influence.
            if (WorldInfluenceMap != null && faction != null)
            {
                WorldInfluenceMap.SetInfluenceData(settlement.Tile, faction, 1.0f); // Full influence on its own tile
                // TODO: Also spread to immediate neighbors to quickly establish a small zone
                // Simplified for now - only influence the settlement's own tile
                NationalBorderDrawer.MarkCacheDirty(); // Borders need to be updated.
                UpdateNationalDataFromInfluenceMap();
            }
        }

        // --- FUTURE ADDITIONS ---
        // Other event-driven methods or global utility functions can be added here as needed.
        // Examples: OnColonyAttacked, OnFactionDefeated, OnPawnDied.
    }
} 