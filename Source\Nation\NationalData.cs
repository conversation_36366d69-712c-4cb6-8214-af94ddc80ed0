using Verse;
using System.Collections.Generic;
using AIGen.NationalSystemMod.Defs; // NEW: Add this for NationalPolicyDef.

namespace AIGen.NationalSystemMod.Nation // New namespace for national data.
{
    // This class holds aggregated data for a specific faction, representing its "national" stats.
    public class NationalData : IExposable
    {
        public Faction Faction; // The faction this NationalData belongs to.

        // Aggregated Influence Data
        public float TotalInfluenceScore; // Sum of influence values from tiles owned by this faction.
        public int ControlledTilesCount; // Number of tiles primarily influenced by this faction.

        // Placeholder for future economic/military data
        public float NationalWealth; // Represents the economic power of the nation.
        public float MilitaryPower; // Represents the military strength of the nation.

        // NEW: Reference to the assigned NationalPolicyDef.
        public NationalPolicyDef CurrentPolicy;

        // Default constructor for Scribe (required for IExposable).
        public NationalData() { }

        // Constructor for creating new NationalData instances.
        public NationalData(Faction faction)
        {
            this.Faction = faction;
            this.TotalInfluenceScore = 0f;
            this.ControlledTilesCount = 0;
            this.NationalWealth = 100f; // Initial placeholder value
            this.MilitaryPower = 50f;   // Initial placeholder value
            this.CurrentPolicy = null; // Will be assigned later.
        }

        // IExposable implementation for saving and loading data.
        public void ExposeData()
        {
            Scribe_References.Look(ref Faction, "faction"); // Save reference to the Faction.
            Scribe_Values.Look(ref TotalInfluenceScore, "totalInfluenceScore", 0f);
            Scribe_Values.Look(ref ControlledTilesCount, "controlledTilesCount", 0);
            Scribe_Values.Look(ref NationalWealth, "nationalWealth", 100f);
            Scribe_Values.Look(ref MilitaryPower, "militaryPower", 50f);
            
            // NEW: Scribe the NationalPolicyDef.
            Scribe_Defs.Look(ref CurrentPolicy, "currentPolicy");

            // Add other data to be saved here as the mod expands.
        }

        // Methods to update or derive values can be added here later.
        public void RecalculateDerivedStats()
        {
            // Example: MilitaryPower might be influenced by NationalWealth or specific Defs.
            // This method would be called after influence is updated.
        }

        // NEW: Check if this NationalData is valid (e.g., linked to a non-null faction).
        public bool IsValid()
        {
            return Faction != null && !Faction.IsHidden && Faction.def.isCivil && CurrentPolicy != null;
        }
    }
} 