using Verse;
using AIGen.NationalSystemMod.Common;
using AIGen.NationalSystemMod.Nation;
using System.Collections.Generic; // Required for using Dictionary.

namespace AIGen.NationalSystemMod.Utils // Or an existing Utilities namespace.
{
    public static class FactionExtensions
    {
        // Extension method to easily get a Faction's NationalData.
        public static NationalData GetNationalData(this Faction faction)
        {
            if (faction != null && CoreLogic.IsModSystemInitialized && CoreLogic.NationalDataByFaction != null && CoreLogic.NationalDataByFaction.TryGetValue(faction, out NationalData nationalData))
            {
                return nationalData;
            }
            return null; // Return null if not found or not initialized.
        }
    }
} 