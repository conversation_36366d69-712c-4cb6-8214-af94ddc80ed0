using Verse;
using <PERSON><PERSON><PERSON>orld; // For IncidentDef, IncidentParms, Messages, MessageTypeDefOf, PawnUtility
using RimWorld.Planet; // For WorldObjectsUtility, Caravan, WorldObject, TileFinder
using UnityEngine; // For Mathf.
using System.Collections.Generic;
using System.Linq;
using AIGen.NationalSystemMod.Common;
using AIGen.NationalSystemMod.Nation;
using AIGen.NationalSystemMod.Utils; // For FactionExtensions
using AIGen.NationalSystemMod.Defs; // NEW: For NationalPolicyDef.
using AIGen.NationalSystemMod.World; // For DiplomaticState.

namespace AIGen.NationalSystemMod.World
{
    // This class manages the spread and decay of influence on the world map.
    public static class InfluenceManager // Static for easy global access.
    {
        // Adjust these constants or make them dynamic based on NationalData.
        private const float BASE_INFLUENCE_SPREAD_RATE = 0.1f; 
        private const float BASE_INFLUENCE_DECAY_RATE = 0.05f;
        private const float MIN_INFLUENCE_THRESHOLD = 0.01f;
        private const float AI_STRATEGY_INTERVAL_DAYS = 5f; // AI re-evaluates strategy every 5 days.
        private static float lastAIStrategyTick = -1f;

        // NEW: Event probabilities/cooldowns
        private const float RAID_CHANCE_ON_CONTEST = 0.05f; // 5% chance for a raid when a tile is contested/taken.
        private const float TRADE_REQUEST_CHANCE_ON_FRIENDLY_BORDER = 0.03f; // 3% chance for trade request.
        private const float DIPLOMATIC_INCIDENT_CHANCE_ON_AGGRESSION = 0.07f; // 7% for a diplomatic incident.
        private const int EVENT_COOLDOWN_TICKS = GenDate.TicksPerDay * 10; // 10 days cooldown between specific events.
        private static Dictionary<Faction, int> _lastAggressiveEventTick = new Dictionary<Faction, int>();

        // --- NEW METHOD: UpdateInfluence() ---
        // This is the main method to be called periodically (e.g., daily).
        public static void UpdateInfluence()
        {
            if (!CoreLogic.IsModSystemInitialized || CoreLogic.WorldInfluenceMap == null || CoreLogic.NationalDataByFaction == null)
            {
                return;
            }

            // Execute AI strategy periodically
            if (lastAIStrategyTick < 0 || GenTicks.TicksGame >= lastAIStrategyTick + GenDate.TicksPerDay * AI_STRATEGY_INTERVAL_DAYS)
            {
                ExecuteAIInfluenceStrategy();
                lastAIStrategyTick = GenTicks.TicksGame;
            }

            Dictionary<int, Pair<Faction, float>> pendingInfluenceChanges = new Dictionary<int, Pair<Faction, float>>();

            // 1. Decay existing influence
            foreach (int tileID in CoreLogic.WorldInfluenceMap.InfluenceDataByTile.Keys.ToList())
            {
                InfluenceTileData data = CoreLogic.WorldInfluenceMap.GetInfluenceData(tileID);
                if (data == null || data.faction == null) continue;

                // NEW: Decay rate influenced by faction's TotalInfluenceScore (stronger nations decay slower).
                float effectiveDecayRate = BASE_INFLUENCE_DECAY_RATE;
                if (CoreLogic.NationalDataByFaction.TryGetValue(data.faction, out NationalData nationalData))
                {
                    // Example: Larger nations might have a reduced decay rate.
                    effectiveDecayRate *= (1f - Mathf.Clamp01(nationalData.TotalInfluenceScore / 100f)); // Max 100 influence for 0 decay
                    if (effectiveDecayRate < 0.01f) effectiveDecayRate = 0.01f; // Minimum decay
                }

                float newAmount = data.influenceAmount - effectiveDecayRate;
                if (newAmount <= MIN_INFLUENCE_THRESHOLD)
                {
                    pendingInfluenceChanges[tileID] = new Pair<Faction, float>(null, 0f); // Mark for removal
                }
                else
                {
                    pendingInfluenceChanges[tileID] = new Pair<Faction, float>(data.faction, newAmount);
                }
            }
            
            // 2. Spread influence from sources (settlements) - REMOVE Player Faction EXCLUSION
            foreach (Settlement settlement in Find.WorldObjects.Settlements)
            {
                // **CHANGED:** No longer excluding player faction.
                // Now considers all non-hidden factions with NationalData.
                if (settlement.Faction != null && !settlement.Faction.Hidden &&
                    CoreLogic.NationalDataByFaction.TryGetValue(settlement.Faction, out NationalData sourceNationalData))
                {
                    float currentTileInfluence = (CoreLogic.WorldInfluenceMap.GetInfluenceData(settlement.Tile)?.influenceAmount ?? 0f);
                    CoreLogic.WorldInfluenceMap.SetInfluenceData(settlement.Tile, settlement.Faction, currentTileInfluence + BASE_INFLUENCE_SPREAD_RATE * 2f);
                    
                    Faction currentTileFaction = CoreLogic.WorldInfluenceMap.GetInfluenceData(settlement.Tile)?.faction;

                    if (currentTileFaction == settlement.Faction || currentTileFaction == null)
                    {
                        // TODO: Implement proper neighbor calculation
                        List<int> neighbors = new List<int>(); // Empty for now

                        float effectiveSpreadAmount = BASE_INFLUENCE_SPREAD_RATE;
                        if(sourceNationalData.CurrentPolicy != null)
                        {
                            effectiveSpreadAmount *= sourceNationalData.CurrentPolicy.influenceSpreadModifier;
                        }
                        effectiveSpreadAmount += (sourceNationalData.NationalWealth / 500f) * 0.1f;
                        effectiveSpreadAmount += (sourceNationalData.MilitaryPower / 200f) * 0.05f;
                        
                        // NEW: Boost influence spread against Rivals/AtWar factions.
                        if (currentTileFaction != null && currentTileFaction != settlement.Faction)
                        {
                            DiplomaticState state = CoreLogic.DiplomacyTracker.GetDiplomaticState(settlement.Faction, currentTileFaction);
                            if (state == DiplomaticState.Rival) effectiveSpreadAmount *= 1.2f; // 20% boost.
                            if (state == DiplomaticState.AtWar) effectiveSpreadAmount *= 1.5f; // 50% boost during war.
                        }

                        effectiveSpreadAmount = Mathf.Clamp(effectiveSpreadAmount, 0.05f, 0.5f);

                        foreach (int neighborID in neighbors)
                        {
                            InfluenceTileData neighborData = CoreLogic.WorldInfluenceMap.GetInfluenceData(neighborID);
                            Pair<Faction, float> existingPending = pendingInfluenceChanges.TryGetValue(neighborID, out var val) ? val : new Pair<Faction, float>(neighborData?.faction, neighborData?.influenceAmount ?? 0f);
                            
                            if (neighborData == null || neighborData.faction == settlement.Faction)
                            {
                                // Neutral or same faction: increase influence
                                float proposedAmount = existingPending.Second + effectiveSpreadAmount;
                                pendingInfluenceChanges[neighborID] = new Pair<Faction, float>(settlement.Faction, proposedAmount);

                                // NEW: Event Trigger - Friendly Border Trade Request
                                if (neighborData != null && neighborData.faction != settlement.Faction && neighborData.faction.IsPlayer && settlement.Faction.PlayerRelationKind == FactionRelationKind.Ally && Rand.Value < TRADE_REQUEST_CHANCE_ON_FRIENDLY_BORDER)
                                {
                                    TryTriggerFriendlyEvent(settlement.Faction, settlement.Tile);
                                }
                            }
                            else // Different faction, contested border
                            {
                                // Store original owner for event triggering.
                                Faction originalOwner = neighborData.faction;

                                if (effectiveSpreadAmount * (sourceNationalData.MilitaryPower / (sourceNationalData.MilitaryPower + originalOwner.GetNationalData().MilitaryPower)) > neighborData.influenceAmount)
                                {
                                     float netInfluence = effectiveSpreadAmount;
                                     if (netInfluence > existingPending.Second || existingPending.First != settlement.Faction) // Take over or add to stronger existing pending
                                     {
                                         pendingInfluenceChanges[neighborID] = new Pair<Faction, float>(settlement.Faction, netInfluence);
                                         
                                         // NEW: Event Trigger - Tile taken over!
                                         if (originalOwner.IsPlayer || settlement.Faction.IsPlayer) // Only trigger if player is involved.
                                         {
                                            TryTriggerContestEvent(settlement.Faction, originalOwner, neighborID, true);
                                         }
                                     }
                                }
                                else
                                {
                                     // If contest reduces influence to near zero for existing owner.
                                     float opponentRemainingInfluence = neighborData.influenceAmount - effectiveSpreadAmount;
                                     if (opponentRemainingInfluence < MIN_INFLUENCE_THRESHOLD)
                                     {
                                         if (existingPending.First != null && existingPending.First == neighborData.faction) // If the target is not already marked for takeover
                                         {
                                            if (existingPending.Second - effectiveSpreadAmount < MIN_INFLUENCE_THRESHOLD)
                                            {
                                                pendingInfluenceChanges[neighborID] = new Pair<Faction, float>(null, 0f);
                                                // Tile became neutral, consider a minor diplomatic event if player was involved
                                                if (originalOwner.IsPlayer || settlement.Faction.IsPlayer)
                                                {
                                                    TryTriggerContestEvent(settlement.Faction, originalOwner, neighborID, false);
                                                }
                                            } else {
                                                pendingInfluenceChanges[neighborID] = new Pair<Faction, float>(existingPending.First, existingPending.Second - effectiveSpreadAmount);
                                                // Still contested, but lost some influence.
                                                if (originalOwner.IsPlayer || settlement.Faction.IsPlayer)
                                                {
                                                    TryTriggerContestEvent(settlement.Faction, originalOwner, neighborID, false); // Small chance of event
                                                }
                                            }
                                         }
                                     }
                                     else if (!pendingInfluenceChanges.ContainsKey(neighborID))
                                     {
                                         pendingInfluenceChanges[neighborID] = new Pair<Faction, float>(neighborData.faction, opponentRemainingInfluence);
                                         // Still contested, but lost some influence.
                                         if (originalOwner.IsPlayer || settlement.Faction.IsPlayer)
                                         {
                                             TryTriggerContestEvent(settlement.Faction, originalOwner, neighborID, false); // Small chance of event
                                         }
                                     }
                                }
                            }
                        }
                    }
                }
            }

            // 3. Apply all pending changes to the WorldInfluenceMap
            bool influenceChanged = false;
            foreach (var entry in pendingInfluenceChanges)
            {
                int tileID = entry.Key;
                Faction faction = entry.Value.First;
                float amount = entry.Value.Second;

                if (faction == null || amount <= MIN_INFLUENCE_THRESHOLD)
                {
                    // If marked for removal or below threshold, remove.
                    if (CoreLogic.WorldInfluenceMap.InfluenceDataByTile.ContainsKey(tileID))
                    {
                        CoreLogic.WorldInfluenceMap.RemoveInfluenceData(tileID);
                        influenceChanged = true;
                    }
                }
                else
                {
                    // Update or set new influence.
                    InfluenceTileData currentData = CoreLogic.WorldInfluenceMap.GetInfluenceData(tileID);
                    if (currentData == null || currentData.faction != faction || !Mathf.Approximately(currentData.influenceAmount, amount))
                    {
                        CoreLogic.WorldInfluenceMap.SetInfluenceData(tileID, faction, amount);
                        influenceChanged = true;
                    }
                }
            }

            if (influenceChanged)
            {
                NationalBorderDrawer.MarkCacheDirty(); // Rebuild borders if influence changed.
            }
        }

        // --- ENHANCED: ExecuteAIInfluenceStrategy() ---
        private static void ExecuteAIInfluenceStrategy()
        {
            foreach (Faction faction in Find.FactionManager.AllFactionsListForReading)
            {
                NationalData nationalData = faction.GetNationalData();
                if (nationalData == null || !nationalData.IsValid() || faction.IsPlayer) continue;
                NationalPolicyDef policy = nationalData.CurrentPolicy;
                if (policy == null) continue;

                List<int> ownedTiles = CoreLogic.WorldInfluenceMap.InfluenceDataByTile
                    .Where(pair => pair.Value.faction == faction)
                    .Select(pair => pair.Key)
                    .ToList();

                List<TileCandidate> candidates = new List<TileCandidate>();

                foreach (int ownedTileID in ownedTiles)
                {
                    // TODO: Implement proper neighbor calculation
                    List<int> neighbors = new List<int>(); // Empty for now

                    foreach (int neighborID in neighbors)
                    {
                        // TODO: Check if tile is passable
                        // if (!Find.World.grid[neighborID].passable) continue;

                        InfluenceTileData neighborInfluence = CoreLogic.WorldInfluenceMap.GetInfluenceData(neighborID);
                        Tile tile = Find.WorldGrid[neighborID];

                        float desirability = 0f;

                        if (neighborInfluence == null) // Neutral tile
                        {
                            desirability += 1.0f * policy.influenceSpreadModifier; 
                        }
                        else if (neighborInfluence.faction != faction) // Enemy tile
                        {
                            DiplomaticState state = CoreLogic.DiplomacyTracker.GetDiplomaticState(faction, neighborInfluence.faction);
                            
                            NationalData otherFactionData = neighborInfluence.faction.GetNationalData();
                            float militaryAdvantageFactor = 1f;
                            if(otherFactionData != null && otherFactionData.MilitaryPower > 0)
                            {
                                militaryAdvantageFactor = (nationalData.MilitaryPower / otherFactionData.MilitaryPower);
                            }

                            // NEW: Major desirability boost if at war or rival.
                            if (state == DiplomaticState.AtWar)
                            {
                                desirability += 5.0f * policy.militaryAggressionFactor * militaryAdvantageFactor; // High priority.
                            }
                            else if (state == DiplomaticState.Rival)
                            {
                                desirability += 2.5f * policy.militaryAggressionFactor * militaryAdvantageFactor; // Moderate priority.
                            }
                            else if (state == DiplomaticState.Hostile) // Standard hostility.
                            {
                                 desirability += 1.5f * policy.militaryAggressionFactor * militaryAdvantageFactor;
                            }
                            else // Peaceful/neutral neighbor, less desirable to attack.
                            {
                                desirability -= 5.0f; // Strongly disincentivize attacking non-hostile foes.
                            }
                        }

                        if (tile.biome.forageability > 0.15f) desirability += 0.5f; 
                        if (tile.hilliness == Hilliness.Mountainous || tile.hilliness == Hilliness.LargeHills) desirability += 0.2f;

                        desirability += Rand.Value * 0.5f;

                        if (desirability > 0f)
                        {
                            candidates.Add(new TileCandidate(neighborID, desirability));
                        }
                    }
                }

                candidates = candidates.OrderByDescending(c => c.Desirability).ToList();

                int tilesToTarget = Mathf.Min(candidates.Count, (int)(nationalData.TotalInfluenceScore / 50f) + 1);
                tilesToTarget = Mathf.Clamp(tilesToTarget, 1, 5); 

                for (int i = 0; i < tilesToTarget && i < candidates.Count; i++)
                {
                    int targetTileID = candidates[i].TileID;
                    float influenceAmount = 0.2f + (nationalData.MilitaryPower / 200f) * 0.1f; 
                    influenceAmount *= policy.influenceSpreadModifier; // Apply policy here too.

                    InfluenceTileData currentTargetInfluence = CoreLogic.WorldInfluenceMap.GetInfluenceData(targetTileID);

                    if (currentTargetInfluence == null || currentTargetInfluence.faction != faction)
                    {
                        float opponentInfluence = currentTargetInfluence?.influenceAmount ?? 0f;
                        
                        if (influenceAmount > opponentInfluence)
                        {
                            CoreLogic.WorldInfluenceMap.SetInfluenceData(targetTileID, faction, influenceAmount);
                        }
                        else
                        {
                            CoreLogic.WorldInfluenceMap.SetInfluenceData(targetTileID, currentTargetInfluence.faction, opponentInfluence - influenceAmount * 0.5f);
                        }
                        NationalBorderDrawer.MarkCacheDirty();
                    }
                }
            }
        }

        // --- NEW: Helper for AI strategy candidates ---
        private class TileCandidate
        {
            public int TileID;
            public float Desirability;
            public TileCandidate(int id, float des) { TileID = id; Desirability = des; }
        }

        // --- NEW METHOD: RequestInfluenceSupport() ---
        // This method will be called when the player requests a faction to project influence.
        public static bool RequestInfluenceSupport(Faction requestingFaction, Faction targetFaction, int targetTileID, float requestedAmount)
        {
            if (requestingFaction == null || targetFaction == null)
            {
                Log.Warning($"[NationalSystemMod.InfluenceManager] Invalid faction in RequestInfluenceSupport. Requesting: {requestingFaction?.Name ?? "NULL"}, Target: {targetFaction?.Name ?? "NULL"}");
                return false;
            }

            // Basic checks:
            // 1. Can the requesting faction request from the target faction? (e.g., friendly relations)
            if (targetFaction.HostileTo(requestingFaction) || targetFaction.IsPlayer) // Cannot request from hostile or player.
            {
                Messages.Message($"Cannot request influence support from {targetFaction.Name}: Not allied or too hostile.", MessageTypeDefOf.RejectInput, false);
                return false;
            }

            // 2. Does the target faction have enough "power" to project influence? (e.g., MilitaryPower or TotalInfluenceScore)
            if (!CoreLogic.NationalDataByFaction.TryGetValue(targetFaction, out NationalData targetNationalData))
            {
                Messages.Message($"Faction {targetFaction.Name} has no national data.", MessageTypeDefOf.RejectInput, false);
                return false;
            }

            // Example cost/prerequisite: Requires a certain amount of wealth/resources or goodwill.
            float cost = requestedAmount * 50f; // Example: 50 silver per influence point.
            // Placeholder: In a real scenario, this would deduct from player's inventory or faction's wealth.
            // For now, just check if enough "theoretical" wealth.
            var map = Find.AnyPlayerHomeMap;
            if (requestingFaction.IsPlayer && (map == null || map.resourceCounter.GetCount(ThingDefOf.Silver) < cost))
            {
                Messages.Message($"Not enough silver to request support ({cost:F0} silver needed).", MessageTypeDefOf.RejectInput, false);
                return false;
            }


            // 3. Simple decision-making for AI: Will the AI faction grant the request?
            // This is a very basic placeholder. AI will grant if relations are good enough and not too far.
            if (targetFaction.PlayerRelationKind != FactionRelationKind.Ally) 
            {
                Messages.Message($"{targetFaction.Name} is not friendly enough to offer influence support.", MessageTypeDefOf.RejectInput, false);
                return false;
            }
            
            // If all checks pass, apply influence directly. This bypasses the spread mechanism for immediate effect.
            // The influence will then decay normally with subsequent updates.
            CoreLogic.WorldInfluenceMap.SetInfluenceData(targetTileID, targetFaction, requestedAmount);
            NationalBorderDrawer.MarkCacheDirty();

            // Deduct cost if player.
            if (requestingFaction.IsPlayer && map != null)
            {
                // TODO: Implement proper resource deduction
                // map.resourceCounter.Silver -= cost;
            }

            Messages.Message($"{targetFaction.Name} has agreed to project {requestedAmount:F2} influence into tile {targetTileID}.", MessageTypeDefOf.PositiveEvent, false);
            Log.Message($"[NationalSystemMod.InfluenceManager] Influence support request from {requestingFaction.Name} to {targetFaction.Name} for tile {targetTileID} granted.");

            // NEW: Event triggered by successful request.
            if (requestingFaction.IsPlayer && !targetFaction.IsPlayer && Rand.Value < 0.2f) // 20% chance of an incident.
            {
                 TryTriggerDiplomaticEvent(requestingFaction, targetFaction);
            }
            return true;
        }

        public static bool PlayerClaimTile(int tileID, float claimAmount)
        {
            Faction playerFaction = Faction.OfPlayer;
            InfluenceTileData originalInfluence = CoreLogic.WorldInfluenceMap?.GetInfluenceData(tileID);
            Faction originalOwner = originalInfluence?.faction;

            // --- Previous logic from existing file ---
            if (playerFaction == null || !CoreLogic.NationalDataByFaction.TryGetValue(playerFaction, out _)) return false;
            float cost = claimAmount * 100f;
            if (originalInfluence != null && originalInfluence.faction != playerFaction) cost *= 2f;

            var map = Find.AnyPlayerHomeMap;
            if (map == null || map.resourceCounter.GetCount(ThingDefOf.Silver) < cost)
            {
                Messages.Message($"Not enough silver to claim this tile ({cost:F0} silver needed).", MessageTypeDefOf.RejectInput, false);
                return false;
            }

            bool isAdjacentToPlayerInfluence = false;
            List<int> playerTiles = CoreLogic.WorldInfluenceMap.InfluenceDataByTile.Where(t => t.Value.faction == playerFaction).Select(t => t.Key).ToList();
            if(!playerTiles.Any())
            {
                 isAdjacentToPlayerInfluence = Find.WorldObjects.Settlements.Any(s => s.Faction == playerFaction && Find.WorldGrid.IsNeighbor(s.Tile, tileID));
            }
            else 
            {
                foreach (int playerInfluencedTile in playerTiles)
                {
                    if (Find.WorldGrid.IsNeighbor(playerInfluencedTile, tileID))
                    {
                        isAdjacentToPlayerInfluence = true;
                        break;
                    }
                }
            }

            if (!isAdjacentToPlayerInfluence)
            {
                Messages.Message("Tile must be adjacent to your influenced territory or settlements to claim.", MessageTypeDefOf.RejectInput, false);
                return false;
            }

            CoreLogic.WorldInfluenceMap.SetInfluenceData(tileID, playerFaction, claimAmount);
            NationalBorderDrawer.MarkCacheDirty();
            // TODO: Implement proper resource deduction
            // map.resourceCounter.Silver -= cost;
            Messages.Message($"Successfully claimed tile {tileID} with {claimAmount:F2} influence for {cost:F0} silver.", MessageTypeDefOf.PositiveEvent, false);
            // --- End of previous logic ---
            
            // NEW: Event triggered by player claim.
            if (originalOwner != null && originalOwner != playerFaction) // If player claimed from an AI faction
            {
                TryTriggerContestEvent(playerFaction, originalOwner, tileID, true); // True for successful takeover.
            }
            return true;
        }

        // --- NEW METHODS: Event Triggering Logic ---
        private static void TryTriggerDiplomaticEvent(Faction factionA, Faction factionB)
        {
            if (factionA == factionB) return;
            if (Rand.Value > DIPLOMATIC_INCIDENT_CHANCE_ON_AGGRESSION) return;
            if ((_lastAggressiveEventTick.TryGetValue(factionA, out int lastTickA) && GenTicks.TicksGame < lastTickA + EVENT_COOLDOWN_TICKS) ||
                (_lastAggressiveEventTick.TryGetValue(factionB, out int lastTickB) && GenTicks.TicksGame < lastTickB + EVENT_COOLDOWN_TICKS)) return;

            IncidentDef incident = null;
            DiplomaticState stateAtoB = CoreLogic.DiplomacyTracker.GetDiplomaticState(factionA, factionB);

            switch (stateAtoB)
            {
                case DiplomaticState.Hostile:
                    incident = IncidentDefOf.RaidEnemy;
                    break;
                case DiplomaticState.Rival:
                    incident = IncidentDefOf.RaidEnemy; // Fallback since FactionArrival doesn't exist
                    break;
                case DiplomaticState.Neutral:
                    if (Rand.Value < 0.5f) incident = IncidentDefOf.VisitorGroup;
                    break;
                case DiplomaticState.Allied:
                    if (Rand.Value < 0.7f) incident = IncidentDefOf.TraderCaravanArrival;
                    break;
            }
            
            if (incident != null)
            {
                Map targetMap = Find.AnyPlayerHomeMap;
                if (targetMap == null) return;
                
                Faction incidentFaction = factionB;
                if(incident == IncidentDefOf.RaidEnemy)
                {
                    incidentFaction = factionA.IsPlayer ? factionB : factionA;
                }
                
                IncidentParms parms = StorytellerUtility.DefaultParmsNow(incident.category, targetMap);
                parms.faction = incidentFaction;
                parms.points = StorytellerUtility.DefaultThreatPointsNow(targetMap);

                if (incident.Worker.CanFireNow(parms))
                {
                    incident.Worker.TryExecute(parms);
                    Messages.Message($"Diplomatic event between {factionA.Name} and {factionB.Name}: {incident.label}", MessageTypeDefOf.NeutralEvent);
                    _lastAggressiveEventTick[factionA] = GenTicks.TicksGame;
                    _lastAggressiveEventTick[factionB] = GenTicks.TicksGame;
                }
            }
        }

        private static void TryTriggerContestEvent(Faction aggressor, Faction defender, int tileID, bool takeoverSuccess)
        {
            if (aggressor == null || defender == null || aggressor == defender) return;
            
            // Basic cooldown check to prevent event spam.
            int lastEventTick = _lastAggressiveEventTick.TryGetValue(aggressor, -1);
            if (GenTicks.TicksGame < lastEventTick + EVENT_COOLDOWN_TICKS) return;

            DiplomaticState state = CoreLogic.DiplomacyTracker.GetDiplomaticState(aggressor, defender);
            // If they are not at war, but aggression is high and goodwill very low, they might declare war.
            if (state != DiplomaticState.AtWar && (state == DiplomaticState.Hostile || state == DiplomaticState.Rival) && Rand.Value < 0.15f) // 15% chance to escalate to war.
            {
                CoreLogic.DiplomacyTracker.DeclareWar(aggressor, defender);
                _lastAggressiveEventTick[aggressor] = GenTicks.TicksGame;
                return; // War declared, no need for simple raid for now.
            }

            // Goodwill impact from the influence contest itself.
            aggressor.TryAffectGoodwillWith(defender, takeoverSuccess ? -5 : -2, true, true, HistoryEventDefOf.UsedHarmfulAbility);
            
            float currentRaidChance = RAID_CHANCE_ON_CONTEST;
            var aggressorData = aggressor.GetNationalData();

            if (aggressorData?.CurrentPolicy != null)
            {
                currentRaidChance *= aggressorData.CurrentPolicy.militaryAggressionFactor;
            }
            if (state == DiplomaticState.Rival) currentRaidChance *= 1.5f;
            if (state == DiplomaticState.Hostile) currentRaidChance *= 2.0f;
            
            if (Rand.Value > currentRaidChance) return;
            if ((_lastAggressiveEventTick.TryGetValue(aggressor, out int lastTickA) && GenTicks.TicksGame < lastTickA + EVENT_COOLDOWN_TICKS) ||
                (_lastAggressiveEventTick.TryGetValue(defender, out int lastTickD) && GenTicks.TicksGame < lastTickD + EVENT_COOLDOWN_TICKS)) return;
            
            IncidentDef incident = null;
            string message = "";
            MessageTypeDef messageType = MessageTypeDefOf.NeutralEvent;

            if (takeoverSuccess) 
            {
                if (defender.IsPlayer)
                {
                    incident = IncidentDefOf.RaidEnemy;
                    message = $"{aggressor.Name} successfully claimed tile {tileID} from you and may press their advantage!";
                    messageType = MessageTypeDefOf.ThreatBig;
                    defender.TryAffectGoodwillWith(aggressor, -20, true, true);
                }
                else if (aggressor.IsPlayer)
                {
                    incident = IncidentDefOf.RaidEnemy;
                    message = $"You claimed tile {tileID} from {defender.Name}. They are preparing a counter-attack!";
                    messageType = MessageTypeDefOf.PositiveEvent;
                    aggressor.TryAffectGoodwillWith(defender, -20, true, true);
                }
                else 
                {
                    defender.TryAffectGoodwillWith(aggressor, -10, true, true);
                    message = $"{aggressor.Name} has taken a tile from {defender.Name}. Their relations have worsened.";
                }
            }
            else 
            {
                if (defender.IsPlayer || aggressor.IsPlayer)
                {
                    message = $"An influence contest at tile {tileID} between {aggressor.Name} and {defender.Name} has escalated.";
                    messageType = MessageTypeDefOf.NeutralEvent;
                    defender.TryAffectGoodwillWith(aggressor, -5, true, false);
                    if (state == DiplomaticState.Hostile) incident = IncidentDefOf.RaidEnemy;
                }
            }

            if (incident != null && (aggressor.IsPlayer || defender.IsPlayer))
            {
                Map targetMap = Find.AnyPlayerHomeMap;
                if (targetMap == null) return;

                IncidentParms parms = StorytellerUtility.DefaultParmsNow(incident.category, targetMap);
                parms.faction = (aggressor.IsPlayer ? defender : aggressor);
                parms.points = StorytellerUtility.DefaultThreatPointsNow(targetMap) * 0.8f;

                if (incident.Worker.CanFireNow(parms))
                {
                    incident.Worker.TryExecute(parms);
                    Messages.Message(message, messageType);
                    _lastAggressiveEventTick[aggressor] = GenTicks.TicksGame;
                    _lastAggressiveEventTick[defender] = GenTicks.TicksGame;
                } else if (!string.IsNullOrEmpty(message)) {
                    Messages.Message(message, messageType);
                }
            } else if (!string.IsNullOrEmpty(message)) {
                Messages.Message(message, messageType);
            }
        }

        private static void TryTriggerFriendlyEvent(Faction faction, int tileID)
        {
            if (Rand.Value > TRADE_REQUEST_CHANCE_ON_FRIENDLY_BORDER) return;
            if (_lastAggressiveEventTick.TryGetValue(faction, out int lastTick) && GenTicks.TicksGame < lastTick + EVENT_COOLDOWN_TICKS) return;

            IncidentDef incident = null;
            if (faction.IsPlayer || Faction.OfPlayer.RelationWith(faction).goodwill > 50)
            {
                incident = new List<IncidentDef>
                {
                    IncidentDefOf.TraderCaravanArrival,
                    IncidentDefOf.VisitorGroup,
                    IncidentDefOf.OrbitalTraderArrival
                }.RandomElement();
            }

            if (incident != null)
            {
                Map targetMap = Find.AnyPlayerHomeMap;
                if (targetMap == null) return;
                
                // Orbital trader requires a beacon, check for it.
                if(incident == IncidentDefOf.OrbitalTraderArrival && !targetMap.listerBuildings.ColonistBuildingsColonistPowered().Any(b => b.def == ThingDefOf.OrbitalTradeBeacon))
                {
                    incident = IncidentDefOf.TraderCaravanArrival; // Fallback to caravan if no beacon
                }

                IncidentParms parms = StorytellerUtility.DefaultParmsNow(incident.category, targetMap);
                parms.faction = faction;
                
                if (incident.Worker.CanFireNow(parms))
                {
                    incident.Worker.TryExecute(parms);
                    Messages.Message($"{faction.Name} has sent a group to reinforce their ties at your border.", MessageTypeDefOf.PositiveEvent);
                    _lastAggressiveEventTick[faction] = GenTicks.TicksGame;
                }
            }
        }

        // --- NEW METHOD: PlayerDemandTile() ---
        // Player demands a tile from another faction.
        public static bool PlayerDemandTile(Faction targetFaction, int tileID)
        {
            Faction playerFaction = Faction.OfPlayer;
            if (playerFaction == null) return false;

            InfluenceTileData tileInfluence = CoreLogic.WorldInfluenceMap.GetInfluenceData(tileID);
            if (tileInfluence == null || tileInfluence.faction != targetFaction)
            {
                Messages.Message($"{targetFaction.Name} does not control tile {tileID}.", MessageTypeDefOf.RejectInput, false);
                return false;
            }

            if (targetFaction.IsPlayer) // Cannot demand from self.
            {
                Messages.Message("Cannot demand tile from your own faction.", MessageTypeDefOf.RejectInput, false);
                return false;
            }

            NationalData playerNationalData = playerFaction.GetNationalData();
            NationalData targetNationalData = targetFaction.GetNationalData();

            if (playerNationalData == null || targetNationalData == null)
            {
                Messages.Message("National data missing for one or both factions.", MessageTypeDefOf.RejectInput, false);
                return false;
            }

            DiplomaticState state = CoreLogic.DiplomacyTracker.GetDiplomaticState(playerFaction, targetFaction);
            float playerStrength = playerNationalData.MilitaryPower;
            float targetStrength = targetNationalData.MilitaryPower;
            float goodwill = playerFaction.GoodwillWith(targetFaction);

            bool adjacent = false;
            foreach (int playerTile in CoreLogic.WorldInfluenceMap.InfluenceDataByTile.Keys.Where(t => CoreLogic.WorldInfluenceMap.GetInfluenceData(t).faction == playerFaction))
            {
                List<int> neighbors = new List<int>();
                Find.WorldGrid.GetDirectlyAdjacentTilesFor(playerTile, out neighbors);
                if (neighbors.Contains(tileID))
                {
                    adjacent = true;
                    break;
                }
            }
            if (!adjacent)
            {
                Messages.Message("Tile must be adjacent to your territory to demand.", MessageTypeDefOf.RejectInput, false);
                return false;
            }

            float successChance = 0.5f; 
            successChance += (playerStrength - targetStrength) * 0.005f; 
            successChance += (goodwill / 100f) * 0.2f;

            if (state == DiplomaticState.Allied) successChance += 0.2f;
            if (state == DiplomaticState.Rival) successChance -= 0.1f;
            if (state == DiplomaticState.Hostile) successChance -= 0.3f;

            successChance = Mathf.Clamp01(successChance);

            if (Rand.Value < successChance)
            {
                CoreLogic.WorldInfluenceMap.SetInfluenceData(tileID, playerFaction, tileInfluence.influenceAmount * 1.2f);
                NationalBorderDrawer.MarkCacheDirty();
                
                int goodwillHit = (state == DiplomaticState.Allied) ? -10 : -15;
                playerFaction.TryAffectGoodwillWith(targetFaction, goodwillHit, true, true, HistoryEventDefOf.DemandedTribute);
                targetFaction.TryAffectGoodwillWith(playerFaction, goodwillHit - 10, true, true, HistoryEventDefOf.DemandedTribute);

                Messages.Message($"{targetFaction.Name} acquiesces to your demand for tile {tileID}!", MessageTypeDefOf.PositiveEvent, false);
                return true;
            }
            else
            {
                playerFaction.TryAffectGoodwillWith(targetFaction, -25, true, true, HistoryEventDefOf.DemandedTribute);
                targetFaction.TryAffectGoodwillWith(playerFaction, -10, true, false); 

                Messages.Message($"{targetFaction.Name} refuses your demand for tile {tileID}! Your relations have worsened.", MessageTypeDefOf.NegativeEvent, false);
                
                if (state == DiplomaticState.Hostile || (state == DiplomaticState.Rival && Rand.Value < 0.5f))
                {
                    TryTriggerContestEvent(targetFaction, playerFaction, tileID, false);
                }
                return false;
            }
        }
    }
} 