using Verse;
using Rim<PERSON>orld; // For Faction
using RimWorld.Planet;
using UnityEngine; // For Color and Material.
using System.Collections.Generic;
using System.Linq;
using AIGen.NationalSystemMod.Common;

namespace AIGen.NationalSystemMod.World // IMPORTANT: Use your actual package ID and include .World.
{
    // This class will be responsible for drawing national borders on the world map.
    // It should be designed to be performant, drawing only necessary lines.
    [StaticConstructorOnStartup]
    public static class NationalBorderDrawer
    {
        // Material for drawing the borders. This needs to be defined in XML (Defs) or created dynamically.
        // For now, we'll use a placeholder.
        private static Material BorderLineMaterial; 

        // Cache for border lines to avoid recalculating every frame.
        // Key: Faction, Value: List of segments (pairs of tile IDs) that form its border.
        private static Dictionary<Faction, List<Pair<int, int>>> _cachedBorderSegments = 
            new Dictionary<Faction, List<Pair<int, int>>>();

        // Flag to indicate if the cache needs to be rebuilt (e.g., when influence changes).
        private static bool _cacheDirty = true;

        static NationalBorderDrawer()
        {
            InitializeMaterials();
        }

        // Call this method to mark the cache as dirty, forcing a rebuild next draw cycle.
        public static void MarkCacheDirty()
        {
            _cacheDirty = true;
        }

        // --- NEW METHOD: InitializeMaterials() ---
        // Call this once during mod initialization to set up the material.
        public static void InitializeMaterials()
        {
            // For now, let's use a simple colored material. In a real mod, you'd load
            // a custom texture for borders.
            if (BorderLineMaterial == null)
            {
                BorderLineMaterial = new Material(ShaderDatabase.WorldOverlayTransparent);
                BorderLineMaterial.renderQueue = WorldObject.RenderQueue + 1; // Draw above world objects
            }
        }

        // --- NEW METHOD: DrawNationalBorders() ---
        // This is the main method that will be called during world map rendering.
        public static void DrawNationalBorders()
        {
            if (WorldRendererUtility.WorldRenderMode != WorldRenderMode.Planet) // Only draw on planet view
            {
                return;
            }

            if (BorderLineMaterial == null) // Ensure material is initialized
            {
                return; 
            }

            // Rebuild cache if dirty (e.g., influence changed)
            if (_cacheDirty)
            {
                RecalculateBorderSegments();
                _cacheDirty = false;
            }

            // Loop through cached segments and draw them.
            // This is optimized because we're drawing pre-calculated segments, not re-evaluating influence.
            foreach (var factionEntry in _cachedBorderSegments)
            {
                Faction faction = factionEntry.Key;
                List<Pair<int, int>> segments = factionEntry.Value;

                // Dynamically set color based on faction.
                // You might want to get a unique color for each faction.
                // For simplicity, using a fixed color for now or faction's ideoligion color.
                if (faction.ideos != null && faction.ideos.PrimaryIdeoligion != null)
                {
                    BorderLineMaterial.color = faction.ideos.PrimaryIdeoligion.Color;
                }
                else
                {
                    BorderLineMaterial.color = Color.Lerp(faction.Color, Color.white, 0.3f); // Lighten faction color
                }
                
                BorderLineMaterial.SetPass(0); // Activate the shader pass

                foreach (var segment in segments)
                {
                    // Draw a line between the two tiles in the segment.
                    // WorldRendererUtility.DrawLineBetweenTiles is efficient for this.
                    WorldRendererUtility.DrawLineBetweenTiles(segment.First, segment.Second, BorderLineMaterial);
                }
            }
        }

        // --- NEW METHOD: RecalculateBorderSegments() ---
        // This method does the heavy lifting of figuring out where borders should be.
        // It's called only when _cacheDirty is true.
        private static void RecalculateBorderSegments()
        {
            _cachedBorderSegments.Clear(); // Clear old cache.

            if (CoreLogic.WorldInfluenceMap == null || CoreLogic.WorldInfluenceMap.InfluenceDataByTile == null) return;

            // Iterate through all influenced tiles from our WorldInfluenceMap.
            foreach (var tileEntry in CoreLogic.WorldInfluenceMap.InfluenceDataByTile)
            {
                int tileID = tileEntry.Key;
                InfluenceTileData tileData = tileEntry.Value;
                
                if (tileData == null || tileData.faction == null) continue;

                // Get neighbors of the current tile.
                List<int> neighbors = new List<int>();
                Find.WorldGrid.GetTileNeighbors(tileID, neighbors);

                foreach (int neighborID in neighbors)
                {
                    InfluenceTileData neighborData = CoreLogic.WorldInfluenceMap.GetInfluenceData(neighborID);

                    // A border exists between two tiles if they are influenced by different factions,
                    // OR if one is influenced and the other is not (neutral).
                    bool isBorder = false;
                    if (neighborData == null || neighborData.faction == null) // Neighbor is neutral
                    {
                        isBorder = true; // Border between influenced and neutral
                    }
                    else if (tileData.faction != neighborData.faction) // Different factions
                    {
                        isBorder = true;
                    }

                    if (isBorder)
                    {
                        // Add this segment to the cache for the current tile's faction.
                        // Ensure unique segments (e.g., (A,B) is same as (B,A)).
                        Faction owningFaction = tileData.faction;
                        if (!_cachedBorderSegments.ContainsKey(owningFaction))
                        {
                            _cachedBorderSegments.Add(owningFaction, new List<Pair<int, int>>());
                        }
                        
                        // Add the segment, ensuring it's unique (e.g., (1,2) vs (2,1)).
                        // Simple way: always add with smaller ID first.
                        int firstTile = Mathf.Min(tileID, neighborID);
                        int secondTile = Mathf.Max(tileID, neighborID);
                        var segment = new Pair<int, int>(firstTile, secondTile);

                        // Only add if not already present (prevents duplicates from neighbor iteration)
                        if (!_cachedBorderSegments[owningFaction].Any(s => s.First == segment.First && s.Second == segment.Second))
                        {
                             _cachedBorderSegments[owningFaction].Add(segment);
                        }
                    }
                }
            }
            Log.Message($"[NationalSystemMod.NationalBorderDrawer] Border segments recalculated for {_cachedBorderSegments.Count} factions.");
        }
    }
} 